// Node Types
export type NodeType = 'input' | 'api' | 'prompt' | 'agent' | 'custom';

export interface Position {
  x: number;
  y: number;
}

// Base Node Definition
export interface BaseNodeData {
  id: string;
  type: NodeType;
  position: Position;
  data: {
    name: string;
    description?: string;
  };
}

// Input Node
export interface InputNodeData extends BaseNodeData {
  type: 'input';
  data: {
    name: string;
    description?: string;
    schema: Record<string, string>;
  };
}

// API Trigger Node
export interface ApiTriggerNodeData extends BaseNodeData {
  type: 'api';
  data: {
    name: string;
    description?: string;
    url: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
    headers?: Record<string, string>;
    authType?: 'none' | 'apiKey' | 'bearer';
    apiKeyHeader?: string;
    credentialId?: number | null;
  };
}

// Prompt Node
export interface PromptNodeData extends BaseNodeData {
  type: 'prompt';
  data: {
    name: string;
    description?: string;
    prompt: string;
    model: string;
    provider: string;
    credentialId: number;
    maxTokens: number;
    temperature: number;
    outputFormat: 'text' | 'json' | 'markdown';
    schema?: Record<string, any>;
  };
}

// Agent Node
export interface AgentNodeData extends BaseNodeData {
  type: 'agent';
  data: {
    name: string;
    description?: string;
    systemPrompt: string;
    model: string;
    provider: string;
    credentialId: number;
    outputFormat: 'text' | 'json';
    schema?: Record<string, any>;
  };
}

// Custom Node
export interface CustomNodeData extends BaseNodeData {
  type: 'custom';
  data: {
    name: string;
    description?: string;
    code: string;
    inputs: { name: string; type: string; required: boolean }[];
    outputs: { name: string; type: string }[];
  };
}

// Union type for all node types
export type NodeData = 
  | InputNodeData 
  | ApiTriggerNodeData 
  | PromptNodeData 
  | AgentNodeData 
  | CustomNodeData;

// Edge definition
export interface Edge {
  id: string;
  source: string;
  target: string;
}

// Workflow definition
export interface Workflow {
  id: number;
  name: string;
  description?: string;
  nodes: Record<string, NodeData>;
  edges: Record<string, Edge>;
  status: 'draft' | 'published';
  isFavorite: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Workflow Run
export interface WorkflowRun {
  id: number;
  workflowId: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: Date;
  endTime: Date | null;
  triggerType: 'manual' | 'api';
  input: any;
  logs: Record<string, any>;
}

// Node Run
export interface NodeRun {
  id: number;
  workflowRunId: number;
  nodeId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: Date;
  endTime: Date | null;
  input: any;
  output: any;
  error: string | null;
}

// AI Credentials
export interface Credential {
  id: number;
  name: string;
  provider: string;
  apiKey: string;
  createdAt: Date;
}
