// Monaco Editor setup for proper worker configuration
import * as monaco from 'monaco-editor';

// Set up Monaco Environment if not already configured
if (typeof window !== 'undefined' && !window.MonacoEnvironment) {
  window.MonacoEnvironment = {
    getWorkerUrl: function (_moduleId: string, label: string) {
      // Use the copied Monaco Editor files in public directory
      const basePath = '/monaco-editor/esm/vs';

      if (label === 'json') {
        return `${basePath}/language/json/json.worker.js`;
      }
      if (label === 'css' || label === 'scss' || label === 'less') {
        return `${basePath}/language/css/css.worker.js`;
      }
      if (label === 'html' || label === 'handlebars' || label === 'razor') {
        return `${basePath}/language/html/html.worker.js`;
      }
      if (label === 'typescript' || label === 'javascript') {
        return `${basePath}/language/typescript/ts.worker.js`;
      }
      return `${basePath}/editor/editor.worker.js`;
    },
    getWorker: function (_moduleId: string, label: string) {
      try {
        const workerUrl = window.MonacoEnvironment?.getWorkerUrl?.(_moduleId, label);
        if (!workerUrl) {
          throw new Error('Worker URL not available');
        }
        return new Worker(workerUrl, {
          type: 'module',
          name: label
        });
      } catch (error) {
        console.warn('Failed to create Monaco worker:', error);
        // Fallback: return a dummy worker that does nothing
        return {
          postMessage: () => {},
          terminate: () => {},
          addEventListener: () => {},
          removeEventListener: () => {}
        } as any;
      }
    }
  };
}

// Configure Monaco Editor themes and languages
export function configureMonaco() {
  // Define custom dark theme
  monaco.editor.defineTheme('custom-dark', {
    base: 'vs-dark',
    inherit: true,
    rules: [
      { token: 'comment', foreground: '6A9955' },
      { token: 'keyword', foreground: '569CD6' },
      { token: 'string', foreground: 'CE9178' },
      { token: 'number', foreground: 'B5CEA8' },
    ],
    colors: {
      'editor.background': '#1e1e1e',
      'editor.foreground': '#d4d4d4',
      'editorLineNumber.foreground': '#858585',
      'editor.selectionBackground': '#264f78',
      'editor.inactiveSelectionBackground': '#3a3d41',
    }
  });

  // Configure JSON language features
  monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
    validate: true,
    allowComments: false,
    schemas: [],
    enableSchemaRequest: false
  });

  // Configure TypeScript/JavaScript language features
  monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
    target: monaco.languages.typescript.ScriptTarget.ES2020,
    allowNonTsExtensions: true,
    moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
    module: monaco.languages.typescript.ModuleKind.CommonJS,
    noEmit: true,
    esModuleInterop: true,
    jsx: monaco.languages.typescript.JsxEmit.React,
    reactNamespace: 'React',
    allowJs: true,
    typeRoots: ['node_modules/@types']
  });

  monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
    noSemanticValidation: false,
    noSyntaxValidation: false,
    onlyVisible: false
  });
}

// Initialize Monaco configuration
configureMonaco();

export default monaco;
