import React from 'react';
import { Node } from 'reactflow';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useQuery } from '@tanstack/react-query';
import { Credential } from '@/types/workflow';
import { X, Settings, ArrowRight, Code, MessageSquare, Bot, Puzzle } from 'lucide-react';

interface NodePropertiesProps {
  node: Node;
  onUpdate: (nodeId: string, data: any) => void;
  onClose: () => void;
  onConfigure: () => void;
}

const NodeProperties: React.FC<NodePropertiesProps> = ({ node, onUpdate, onClose, onConfigure }) => {
  const { data: credentials } = useQuery<Credential[]>({
    queryKey: ['/api/credentials'],
  });
  
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onUpdate(node.id, { ...node.data, name: e.target.value });
  };
  
  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onUpdate(node.id, { ...node.data, description: e.target.value });
  };
  
  const getNodeIcon = () => {
    switch (node.type) {
      case 'input':
        return <ArrowRight className="text-[#0078D4]" />;
      case 'api':
        return <Code className="text-[#5C2D91]" />;
      case 'prompt':
        return <MessageSquare className="text-[#FF8C00]" />;
      case 'agent':
        return <Bot className="text-[#107C10]" />;
      case 'custom':
        return <Puzzle className="text-[#D83B01]" />;
      default:
        return <Settings />;
    }
  };
  
  return (
    <div className="w-80 bg-white dark:bg-neutral-800 border-l border-neutral-200 dark:border-neutral-700 flex flex-col">
      <div className="p-4 border-b border-neutral-200 dark:border-neutral-700 flex justify-between items-center">
        <h3 className="font-medium text-neutral-800 dark:text-white flex items-center">
          {getNodeIcon()}
          <span className="ml-2">{node.data.name || `${node.type.charAt(0).toUpperCase() + node.type.slice(1)} Node`}</span>
        </h3>
        <button 
          onClick={onClose}
          className="text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-200"
        >
          <X size={18} />
        </button>
      </div>
      
      <div className="p-4 overflow-y-auto scrollbar-thin flex-1">
        <div className="space-y-4">
          <div>
            <Label htmlFor="node-name">Node Name</Label>
            <Input
              id="node-name"
              value={node.data.name || ''}
              onChange={handleNameChange}
              className="mt-1"
            />
          </div>
          
          <div>
            <Label htmlFor="node-description">Description (optional)</Label>
            <Textarea
              id="node-description"
              value={node.data.description || ''}
              onChange={handleDescriptionChange}
              className="mt-1"
              rows={2}
            />
          </div>
          
          {node.type === 'input' && (
            <div>
              <Label>Input Schema</Label>
              <div className="mt-1 p-2 bg-neutral-50 dark:bg-neutral-900 rounded text-xs font-mono">
                {JSON.stringify(node.data.schema || { "query": "string" }, null, 2)}
              </div>
              <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                Configure the schema in the advanced settings
              </p>
            </div>
          )}
          
          {node.type === 'prompt' && (
            <>
              <div>
                <Label>LLM Model</Label>
                <Select
                  value={node.data.model || ''}
                  onValueChange={(value) => onUpdate(node.id, { ...node.data, model: value })}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select a model" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Gemini Pro">Gemini Pro</SelectItem>
                    <SelectItem value="Gemini Flash">Gemini Flash</SelectItem>
                    <SelectItem value="Claude 3 Opus">Claude 3 Opus</SelectItem>
                    <SelectItem value="Claude 3 Sonnet">Claude 3 Sonnet</SelectItem>
                    <SelectItem value="GPT-4">GPT-4</SelectItem>
                    <SelectItem value="GPT-3.5">GPT-3.5</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label>Prompt Template</Label>
                <Textarea
                  value={node.data.prompt || ''}
                  onChange={(e) => onUpdate(node.id, { ...node.data, prompt: e.target.value })}
                  className="mt-1 font-mono"
                  rows={3}
                />
              </div>
            </>
          )}
          
          {node.type === 'agent' && (
            <>
              <div>
                <Label>LLM Model</Label>
                <Select
                  value={node.data.model || ''}
                  onValueChange={(value) => onUpdate(node.id, { ...node.data, model: value })}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select a model" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Claude 3 Opus">Claude 3 Opus</SelectItem>
                    <SelectItem value="Claude 3 Sonnet">Claude 3 Sonnet</SelectItem>
                    <SelectItem value="GPT-4">GPT-4</SelectItem>
                    <SelectItem value="Gemini Pro">Gemini Pro</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label>System Prompt</Label>
                <Textarea
                  value={node.data.systemPrompt || ''}
                  onChange={(e) => onUpdate(node.id, { ...node.data, systemPrompt: e.target.value })}
                  className="mt-1 font-mono"
                  rows={3}
                />
              </div>
              
              <div>
                <Label>Output Format</Label>
                <RadioGroup 
                  value={node.data.outputFormat || 'text'} 
                  onValueChange={(value) => onUpdate(node.id, { ...node.data, outputFormat: value })}
                  className="flex space-x-4 mt-1"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="text" id="text" />
                    <Label htmlFor="text">Text</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="json" id="json" />
                    <Label htmlFor="json">JSON</Label>
                  </div>
                </RadioGroup>
              </div>
            </>
          )}
          
          {node.type === 'api' && (
            <>
              <div>
                <Label>API Endpoint</Label>
                <Input
                  value={node.data.url || ''}
                  onChange={(e) => onUpdate(node.id, { ...node.data, url: e.target.value })}
                  className="mt-1"
                  placeholder="https://api.example.com/endpoint"
                />
              </div>
              
              <div>
                <Label>HTTP Method</Label>
                <Select
                  value={node.data.method || 'POST'}
                  onValueChange={(value) => onUpdate(node.id, { ...node.data, method: value })}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="GET">GET</SelectItem>
                    <SelectItem value="POST">POST</SelectItem>
                    <SelectItem value="PUT">PUT</SelectItem>
                    <SelectItem value="DELETE">DELETE</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </>
          )}
        </div>
      </div>
      
      <div className="p-4 border-t border-neutral-200 dark:border-neutral-700">
        <Button onClick={onConfigure} className="w-full">
          <Settings size={16} className="mr-2" />
          Advanced Configuration
        </Button>
      </div>
    </div>
  );
};

export default NodeProperties;
