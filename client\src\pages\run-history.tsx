import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { WorkflowRun, Workflow } from '@/types/workflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Download, 
  Eye, 
  Calendar, 
  Search 
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { format, formatDistanceToNow } from 'date-fns';
import RunStatusModal from '@/components/workflows/modals/RunStatusModal';
import { Skeleton } from '@/components/ui/skeleton';

const RunHistory: React.FC = () => {
  const [selectedWorkflowId, setSelectedWorkflowId] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedRun, setSelectedRun] = useState<{runId: number, workflowId: number} | null>(null);
  
  // Fetch workflows
  const { data: workflows, isLoading: isWorkflowsLoading } = useQuery<Workflow[]>({
    queryKey: ['/api/workflows'],
  });
  
  // Fetch workflow runs
  const { data: workflowRuns, isLoading: isRunsLoading } = useQuery<WorkflowRun[]>({
    queryKey: ['/api/workflow-runs'],
  });
  
  // Filter runs based on selected workflow and search term
  const filteredRuns = workflowRuns?.filter(run => {
    const matchesWorkflow = selectedWorkflowId === 'all' || run.workflowId.toString() === selectedWorkflowId;
    const matchesSearch = !searchTerm || 
      (workflows?.find(w => w.id === run.workflowId)?.name.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesWorkflow && matchesSearch;
  }) || [];
  
  // Sort runs by start time (latest first)
  const sortedRuns = [...filteredRuns].sort((a, b) => 
    new Date(b.startTime).getTime() - new Date(a.startTime).getTime()
  );
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <Badge variant="outline" className="bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400 border-green-200 dark:border-green-800">
            <CheckCircle className="mr-1 h-3 w-3" />
            Completed
          </Badge>
        );
      case 'failed':
        return (
          <Badge variant="outline" className="bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400 border-red-200 dark:border-red-800">
            <XCircle className="mr-1 h-3 w-3" />
            Failed
          </Badge>
        );
      case 'running':
        return (
          <Badge variant="outline" className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400 border-blue-200 dark:border-blue-800">
            <Clock className="mr-1 h-3 w-3 animate-pulse" />
            Running
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-400">
            {status}
          </Badge>
        );
    }
  };
  
  const getDuration = (run: WorkflowRun) => {
    if (!run.endTime) return 'Running...';
    
    const start = new Date(run.startTime).getTime();
    const end = new Date(run.endTime).getTime();
    const durationMs = end - start;
    
    if (durationMs < 1000) return `${durationMs}ms`;
    return `${(durationMs / 1000).toFixed(1)}s`;
  };
  
  const handleViewRun = (runId: number, workflowId: number) => {
    setSelectedRun({ runId, workflowId });
  };
  
  const isLoading = isWorkflowsLoading || isRunsLoading;
  
  return (
    <div className="p-2">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-neutral-800 dark:text-white">Run History</h1>
        <Button variant="outline" className="flex items-center">
          <Download className="w-4 h-4 mr-2" />
          Export History
        </Button>
      </div>
      
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <label className="text-sm font-medium mb-1 block text-neutral-700 dark:text-neutral-300">Workflow</label>
              <Select value={selectedWorkflowId} onValueChange={setSelectedWorkflowId}>
                <SelectTrigger>
                  <SelectValue placeholder="Select workflow" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Workflows</SelectItem>
                  {workflows?.map(workflow => (
                    <SelectItem key={workflow.id} value={workflow.id.toString()}>
                      {workflow.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex-1">
              <label className="text-sm font-medium mb-1 block text-neutral-700 dark:text-neutral-300">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400" size={16} />
                <Input
                  className="pl-9"
                  placeholder="Search by workflow name"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            
            <div className="flex-1">
              <label className="text-sm font-medium mb-1 block text-neutral-700 dark:text-neutral-300">Date Range</label>
              <div className="flex items-center border rounded-md px-3 py-2 text-sm text-neutral-700 dark:text-neutral-300">
                <Calendar className="w-4 h-4 mr-2 text-neutral-400" />
                <span>Last 30 days</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Workflow Runs</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              {[1, 2, 3, 4, 5].map((i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          ) : sortedRuns.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Run ID</TableHead>
                    <TableHead>Workflow</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Started</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Trigger</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedRuns.map(run => {
                    const workflow = workflows?.find(w => w.id === run.workflowId);
                    return (
                      <TableRow key={run.id}>
                        <TableCell className="font-mono text-sm">{run.id}</TableCell>
                        <TableCell className="font-medium">{workflow?.name || `Workflow #${run.workflowId}`}</TableCell>
                        <TableCell>{getStatusBadge(run.status)}</TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="text-sm">{format(new Date(run.startTime), 'MMM d, yyyy')}</span>
                            <span className="text-xs text-neutral-500 dark:text-neutral-400">
                              {format(new Date(run.startTime), 'h:mm a')}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>{getDuration(run)}</TableCell>
                        <TableCell className="capitalize">{run.triggerType}</TableCell>
                        <TableCell className="text-right">
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleViewRun(run.id, run.workflowId)}
                          >
                            <Eye className="w-4 h-4 mr-1" />
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12 border border-dashed border-neutral-300 dark:border-neutral-700 rounded-lg">
              <Clock className="mx-auto h-12 w-12 text-neutral-400" />
              <p className="mt-4 text-neutral-600 dark:text-neutral-400">No workflow runs found</p>
              <p className="text-sm text-neutral-500 dark:text-neutral-500">
                Execute a workflow to see runs appear here
              </p>
            </div>
          )}
        </CardContent>
      </Card>
      
      {selectedRun && workflows && (
        <RunStatusModal
          workflow={workflows.find(w => w.id === selectedRun.workflowId)!}
          runId={selectedRun.runId}
          onClose={() => setSelectedRun(null)}
        />
      )}
    </div>
  );
};

export default RunHistory;
