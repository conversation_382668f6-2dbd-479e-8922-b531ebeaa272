import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ThemeProvider } from "@/components/ThemeProvider";
import AppLayout from "@/components/layout/AppLayout";
import NotFound from "@/pages/not-found";

// Import pages
import Home from "@/pages/home";
import WorkflowsPage from "@/pages/workflows";
import WorkflowEditor from "@/pages/workflows/editor";
import RunHistory from "@/pages/run-history";
import Analytics from "@/pages/analytics";
import Credentials from "@/pages/credentials";
import Settings from "@/pages/settings";

function Router() {
  return (
    <AppLayout>
      <Switch>
        <Route path="/" component={WorkflowsPage} />
        <Route path="/workflows" component={WorkflowsPage} />
        <Route path="/workflows/editor" component={WorkflowEditor} />
        <Route path="/workflows/editor/:id" component={WorkflowEditor} />
        <Route path="/run-history" component={RunHistory} />
        <Route path="/analytics" component={Analytics} />
        <Route path="/credentials" component={Credentials} />
        <Route path="/settings" component={Settings} />
        <Route component={NotFound} />
      </Switch>
    </AppLayout>
  );
}

function App() {
  return (
    <ThemeProvider defaultTheme="light">
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Toaster />
          <Router />
        </TooltipProvider>
      </QueryClientProvider>
    </ThemeProvider>
  );
}

export default App;
