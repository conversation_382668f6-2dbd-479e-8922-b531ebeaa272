import React from 'react';
import { <PERSON> } from 'wouter';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowR<PERSON>, Cpu, History, Key, BarChart2 } from 'lucide-react';

const Home: React.FC = () => {
  return (
    <div className="p-2">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-neutral-900 dark:text-white mb-4">AI Workflow Builder</h1>
        <p className="text-xl text-neutral-600 dark:text-neutral-300 max-w-2xl mx-auto">
          Create, manage, and execute AI-powered workflow automations with a visual interface
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
        <Card>
          <CardHeader className="pb-2">
            <Cpu className="h-12 w-12 text-primary mb-2" />
            <CardTitle>Workflows</CardTitle>
            <CardDescription>Create and manage your workflows</CardDescription>
          </CardHeader>
          <CardContent className="text-sm text-neutral-600 dark:text-neutral-400">
            Build visual workflows with various node types including Input, API Trigger, Prompt, and Agent nodes.
          </CardContent>
          <CardFooter>
            <Link href="/workflows">
              <Button className="w-full">
                Manage Workflows <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardFooter>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <History className="h-12 w-12 text-orange-500 mb-2" />
            <CardTitle>Run History</CardTitle>
            <CardDescription>View past workflow executions</CardDescription>
          </CardHeader>
          <CardContent className="text-sm text-neutral-600 dark:text-neutral-400">
            Track and analyze execution history, review input/output data, and troubleshoot errors.
          </CardContent>
          <CardFooter>
            <Link href="/run-history">
              <Button variant="outline" className="w-full">
                View History <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardFooter>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <Key className="h-12 w-12 text-green-500 mb-2" />
            <CardTitle>Credentials</CardTitle>
            <CardDescription>Manage your API keys securely</CardDescription>
          </CardHeader>
          <CardContent className="text-sm text-neutral-600 dark:text-neutral-400">
            Securely store and manage your API keys for various LLM providers like Google Gemini and OpenRouter.
          </CardContent>
          <CardFooter>
            <Link href="/credentials">
              <Button variant="outline" className="w-full">
                Manage Credentials <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardFooter>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <BarChart2 className="h-12 w-12 text-purple-500 mb-2" />
            <CardTitle>Analytics</CardTitle>
            <CardDescription>Performance insights</CardDescription>
          </CardHeader>
          <CardContent className="text-sm text-neutral-600 dark:text-neutral-400">
            View metrics and analytics about your workflows, including run counts, error rates, and execution times.
          </CardContent>
          <CardFooter>
            <Link href="/analytics">
              <Button variant="outline" className="w-full">
                View Analytics <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
      
      <div className="bg-neutral-50 dark:bg-neutral-800 rounded-lg p-8 text-center">
        <h2 className="text-2xl font-bold text-neutral-900 dark:text-white mb-4">Get Started</h2>
        <p className="text-neutral-600 dark:text-neutral-300 mb-6 max-w-2xl mx-auto">
          Create your first workflow or explore the examples to see what's possible.
        </p>
        <div className="flex justify-center gap-4">
          <Link href="/workflows/editor">
            <Button size="lg">
              Create New Workflow
            </Button>
          </Link>
          <Link href="/workflows">
            <Button variant="outline" size="lg">
              Browse Workflows
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Home;
