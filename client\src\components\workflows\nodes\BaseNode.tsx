import React from 'react';
import { Handle, Position, NodeProps } from 'reactflow';

export interface BaseNodeProps extends NodeProps {
  data: {
    name: string;
    description?: string;
  };
  icon: React.ReactNode;
  color: string;
  hasInput?: boolean;
  hasOutput?: boolean;
  children?: React.ReactNode;
}

const BaseNode: React.FC<BaseNodeProps> = ({ 
  id,
  data,
  icon,
  color,
  hasInput = true,
  hasOutput = true,
  selected,
  children,
  dragging,
  type,
  xPos,
  yPos,
  zIndex,
  isConnectable,
  targetPosition,
  sourcePosition
}) => {
  return (
    <div className={`w-64 bg-white dark:bg-neutral-800 rounded-lg shadow-md border-2 ${selected ? `border-${color}` : 'border-neutral-200 dark:border-neutral-700'} overflow-hidden`}>
      <div className={`bg-[${color}] px-3 py-2 text-white flex items-center justify-between`}>
        <div className="flex items-center">
          {icon}
          <span className="font-medium ml-2">{data.name || 'Unnamed Node'}</span>
        </div>
        <div className="flex items-center space-x-1">
          <button className={`p-1 hover:bg-${color}/80 rounded`}>
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="3" />
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
            </svg>
          </button>
          <button className={`p-1 hover:bg-${color}/80 rounded`}>
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="1" />
              <circle cx="19" cy="12" r="1" />
              <circle cx="5" cy="12" r="1" />
            </svg>
          </button>
        </div>
      </div>
      
      <div className="p-3">
        <div className="text-sm font-medium text-neutral-800 dark:text-white">{data.name || 'Unnamed Node'}</div>
        {data.description && (
          <div className="mt-1.5 text-xs text-neutral-500 dark:text-neutral-400">{data.description}</div>
        )}
        {/* Node-specific content rendered via children prop */}
        {children}
      </div>
      
      <div className="px-3 py-2 bg-neutral-50 dark:bg-neutral-900 border-t border-neutral-200 dark:border-neutral-700 flex justify-between">
        {hasInput && (
          <Handle
            type="target"
            position={Position.Left}
            id="input"
            className={`w-3 h-3 bg-[${color}] border-2 border-white dark:border-neutral-800`}
          />
        )}
        {hasOutput && (
          <Handle
            type="source"
            position={Position.Right}
            id="output"
            className={`w-3 h-3 bg-[${color}] border-2 border-white dark:border-neutral-800`}
          />
        )}
      </div>
    </div>
  );
};

export default BaseNode;
