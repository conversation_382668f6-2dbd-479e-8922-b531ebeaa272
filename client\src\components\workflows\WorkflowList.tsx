import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Workflow } from '@/types/workflow';
import WorkflowItem from './WorkflowItem';
import { Button } from '@/components/ui/button';
import { Link } from 'wouter';
import { Plus, Filter, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';

const WorkflowList: React.FC = () => {
  const [filter, setFilter] = useState<'all' | 'favorites' | 'published'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  
  const { data: workflows, isLoading } = useQuery<Workflow[]>({
    queryKey: ['/api/workflows'],
  });

  const filteredWorkflows = workflows?.filter(workflow => {
    // Filter by status
    if (filter === 'favorites' && !workflow.isFavorite) return false;
    if (filter === 'published' && workflow.status !== 'published') return false;
    
    // Filter by search term
    if (searchTerm && !workflow.name.toLowerCase().includes(searchTerm.toLowerCase())) return false;
    
    return true;
  });

  return (
    <div className="p-2">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-neutral-800 dark:text-white">Workflows</h1>
        <Link href="/workflows/editor">
          <Button className="bg-primary hover:bg-primary/90">
            <Plus className="w-4 h-4 mr-2" />
            New Workflow
          </Button>
        </Link>
      </div>
      
      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400" size={18} />
          <Input
            className="pl-10"
            placeholder="Search workflows..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>
      
      <div className="flex space-x-2 mb-6">
        <Button
          variant={filter === 'all' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setFilter('all')}
          className="rounded-full"
        >
          All
        </Button>
        <Button
          variant={filter === 'favorites' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setFilter('favorites')}
          className="rounded-full"
        >
          Favorites
        </Button>
        <Button
          variant={filter === 'published' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setFilter('published')}
          className="rounded-full"
        >
          Published
        </Button>
        <Button variant="outline" size="sm" className="rounded-full ml-auto">
          <Filter className="w-4 h-4 mr-2" />
          More Filters
        </Button>
      </div>
      
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="border border-neutral-200 dark:border-neutral-700 rounded-lg p-4">
              <Skeleton className="h-6 w-2/3 mb-2" />
              <Skeleton className="h-4 w-1/2 mb-4" />
              <div className="flex space-x-2">
                <Skeleton className="h-5 w-16 rounded-full" />
                <Skeleton className="h-5 w-12" />
              </div>
            </div>
          ))}
        </div>
      ) : filteredWorkflows?.length ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredWorkflows.map((workflow) => (
            <WorkflowItem
              key={workflow.id}
              workflow={workflow}
              linkToEditor={true}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border border-dashed border-neutral-300 dark:border-neutral-700 rounded-lg">
          <p className="text-neutral-500 dark:text-neutral-400 mb-4">No workflows found</p>
          <Link href="/workflows/editor">
            <Button variant="outline">
              <Plus className="w-4 h-4 mr-2" />
              Create New Workflow
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
};

export default WorkflowList;
