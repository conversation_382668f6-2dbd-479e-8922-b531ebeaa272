import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Credential } from '@/types/workflow';
import { apiRequest } from '@/lib/queryClient';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Key,
  Plus,
  Edit,
  Trash2,
  Copy,
  CheckCircle,
  Info,
  AlertCircle
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { Skeleton } from '@/components/ui/skeleton';

const CredentialsPage: React.FC = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editingCredential, setEditingCredential] = useState<Credential | null>(null);
  const [deletingCredentialId, setDeletingCredentialId] = useState<number | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    provider: 'google',
    apiKey: ''
  });

  // Fetch credentials
  const { data: credentials, isLoading } = useQuery<Credential[]>({
    queryKey: ['/api/credentials'],
  });

  // Create credential mutation
  const createCredentialMutation = useMutation({
    mutationFn: async (data: { name: string; provider: string; apiKey: string }) => {
      return apiRequest('POST', '/api/credentials', data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/credentials'] });
      setIsDialogOpen(false);
      resetForm();
      toast({
        title: "Credential created",
        description: "Your API credential has been saved securely",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to create credential",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Update credential mutation
  const updateCredentialMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: Partial<Credential> }) => {
      return apiRequest('PATCH', `/api/credentials/${id}`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/credentials'] });
      setIsDialogOpen(false);
      resetForm();
      toast({
        title: "Credential updated",
        description: "Your API credential has been updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to update credential",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Delete credential mutation
  const deleteCredentialMutation = useMutation({
    mutationFn: async (id: number) => {
      return apiRequest('DELETE', `/api/credentials/${id}`, {});
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/credentials'] });
      setIsDeleteDialogOpen(false);
      toast({
        title: "Credential deleted",
        description: "Your API credential has been removed",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to delete credential",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const resetForm = () => {
    setFormData({
      name: '',
      provider: 'google',
      apiKey: ''
    });
    setEditingCredential(null);
  };

  const handleDialogOpen = (open: boolean) => {
    setIsDialogOpen(open);
    if (!open) {
      resetForm();
    }
  };

  const handleDeleteDialogOpen = (open: boolean) => {
    setIsDeleteDialogOpen(open);
    if (!open) {
      setDeletingCredentialId(null);
    }
  };

  const handleEdit = (credential: Credential) => {
    setEditingCredential(credential);
    setFormData({
      name: credential.name,
      provider: credential.provider,
      apiKey: '' // Don't show the actual API key
    });
    setIsDialogOpen(true);
  };

  const handleDelete = (id: number) => {
    setDeletingCredentialId(id);
    setIsDeleteDialogOpen(true);
  };

  const handleFormChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = () => {
    // Validate form
    if (!formData.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Please provide a name for the credential",
        variant: "destructive",
      });
      return;
    }

    if (!formData.apiKey.trim() && !editingCredential) {
      toast({
        title: "Validation Error",
        description: "Please provide an API key",
        variant: "destructive",
      });
      return;
    }

    if (editingCredential) {
      // Update existing credential
      const updateData: Partial<Credential> = {
        name: formData.name,
        provider: formData.provider
      };

      // Only include API key if it was changed
      if (formData.apiKey.trim()) {
        updateData.apiKey = formData.apiKey;
      }

      updateCredentialMutation.mutate({
        id: editingCredential.id,
        data: updateData
      });
    } else {
      // Create new credential
      createCredentialMutation.mutate(formData);
    }
  };

  const handleCopyKey = (maskedKey: string) => {
    // This is just visual feedback since we can't actually copy the full key
    toast({
      title: "Cannot copy full API key",
      description: "For security reasons, the complete API key is not accessible after creation",
    });
  };

  const getProviderLabel = (provider: string) => {
    switch (provider) {
      case 'google':
        return 'Google AI';
      case 'openrouter':
        return 'OpenRouter';
      default:
        return provider;
    }
  };

  return (
    <div className="p-2">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-neutral-800 dark:text-white">Credentials</h1>
        <Button onClick={() => {
          resetForm();
          setIsDialogOpen(true);
        }}>
          <Plus className="w-4 h-4 mr-2" />
          Add Credential
        </Button>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>API Credentials</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          ) : credentials && credentials.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Provider</TableHead>
                  <TableHead>API Key</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {credentials.map(credential => (
                  <TableRow key={credential.id}>
                    <TableCell className="font-medium">{credential.name}</TableCell>
                    <TableCell>{getProviderLabel(credential.provider)}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <span className="font-mono">
                          {credential.apiKey.substring(0, 4)}...{credential.apiKey.substring(credential.apiKey.length - 4)}
                        </span>
                        <button
                          onClick={() => handleCopyKey(credential.apiKey)}
                          className="ml-2 text-neutral-500 hover:text-neutral-700 dark:hover:text-neutral-300"
                          title="Copy API key"
                        >
                          <Copy className="w-4 h-4" />
                        </button>
                      </div>
                    </TableCell>
                    <TableCell>{format(new Date(credential.createdAt), 'MMM d, yyyy')}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(credential)}
                        className="mr-1"
                      >
                        <Edit className="w-4 h-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(credential.id)}
                      >
                        <Trash2 className="w-4 h-4 text-red-500" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-12 border border-dashed border-neutral-300 dark:border-neutral-700 rounded-lg">
              <Key className="mx-auto h-12 w-12 text-neutral-400" />
              <p className="mt-4 text-neutral-600 dark:text-neutral-400">No credentials found</p>
              <p className="text-sm text-neutral-500 dark:text-neutral-500 mb-4">
                Add API credentials to connect to AI providers
              </p>
              <Button onClick={() => setIsDialogOpen(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Credential
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="mt-8">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Available AI Providers</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="google">
              <TabsList className="mb-4">
                <TabsTrigger value="google">Google AI</TabsTrigger>
                <TabsTrigger value="openrouter">OpenRouter</TabsTrigger>
              </TabsList>

              <TabsContent value="google">
                <div className="space-y-4">
                  <div className="flex items-start space-x-4">
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded">
                      <Key className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium">Google AI API</h3>
                      <p className="text-neutral-600 dark:text-neutral-400 text-sm">
                        Access Google's Gemini models for text generation and reasoning
                      </p>
                      <div className="flex items-center mt-2">
                        <a
                          href="https://ai.google.dev/"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary text-sm hover:underline"
                        >
                          Get API key
                        </a>
                        <span className="mx-2 text-neutral-300 dark:text-neutral-600">•</span>
                        <a
                          href="https://ai.google.dev/docs"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary text-sm hover:underline"
                        >
                          Documentation
                        </a>
                      </div>
                    </div>
                  </div>

                  <div className="bg-neutral-50 dark:bg-neutral-800 p-3 rounded-md border border-neutral-200 dark:border-neutral-700">
                    <div className="flex items-center text-sm">
                      <Info className="h-4 w-4 text-blue-500 mr-2" />
                      <span className="font-medium">Available Models:</span>
                    </div>
                    <ul className="mt-2 text-sm space-y-1 ml-6 list-disc text-neutral-700 dark:text-neutral-300">
                      <li>Gemini Pro - General purpose model for text generation</li>
                      <li>Gemini Flash - Faster, optimized model for quick responses</li>
                    </ul>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="openrouter">
                <div className="space-y-4">
                  <div className="flex items-start space-x-4">
                    <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded">
                      <Key className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium">OpenRouter</h3>
                      <p className="text-neutral-600 dark:text-neutral-400 text-sm">
                        Single API to access multiple AI models including Claude, GPT-4, and more
                      </p>
                      <div className="flex items-center mt-2">
                        <a
                          href="https://openrouter.ai/"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary text-sm hover:underline"
                        >
                          Get API key
                        </a>
                        <span className="mx-2 text-neutral-300 dark:text-neutral-600">•</span>
                        <a
                          href="https://openrouter.ai/docs"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary text-sm hover:underline"
                        >
                          Documentation
                        </a>
                      </div>
                    </div>
                  </div>

                  <div className="bg-neutral-50 dark:bg-neutral-800 p-3 rounded-md border border-neutral-200 dark:border-neutral-700">
                    <div className="flex items-center text-sm">
                      <Info className="h-4 w-4 text-purple-500 mr-2" />
                      <span className="font-medium">Available Models:</span>
                    </div>
                    <ul className="mt-2 text-sm space-y-1 ml-6 list-disc text-neutral-700 dark:text-neutral-300">
                      <li>Claude 3 Opus - Anthropic's most powerful model</li>
                      <li>Claude 3 Sonnet - Balanced performance and speed</li>
                      <li>GPT-4 - OpenAI's advanced reasoning model</li>
                      <li>GPT-3.5 - Efficient, cost-effective model</li>
                      <li>And many more via the OpenRouter API</li>
                    </ul>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* Add/Edit Credential Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={handleDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingCredential ? 'Edit Credential' : 'Add New Credential'}
            </DialogTitle>
            <DialogDescription>
              {editingCredential
                ? 'Update the details for this API credential. Leave the API key blank to keep the current key.'
                : 'Add a new API credential to connect to AI providers for your workflows.'
              }
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Credential Name</Label>
              <Input
                id="name"
                placeholder="e.g., Google AI Key"
                value={formData.name}
                onChange={(e) => handleFormChange('name', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="provider">AI Provider</Label>
              <Select
                value={formData.provider}
                onValueChange={(value) => handleFormChange('provider', value)}
              >
                <SelectTrigger id="provider">
                  <SelectValue placeholder="Select provider" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="google">Google AI</SelectItem>
                  <SelectItem value="openrouter">OpenRouter</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="apiKey">
                API Key {editingCredential && '(leave blank to keep current key)'}
              </Label>
              <Input
                id="apiKey"
                type="password"
                placeholder={editingCredential ? '••••••••••••••••' : 'Enter API key'}
                value={formData.apiKey}
                onChange={(e) => handleFormChange('apiKey', e.target.value)}
              />
            </div>

            {formData.provider === 'google' && (
              <div className="flex items-center p-3 bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 rounded text-sm">
                <Info className="h-4 w-4 mr-2 flex-shrink-0" />
                <span>Get your API key from the Google AI Studio at <a href="https://ai.google.dev/" target="_blank" rel="noopener noreferrer" className="underline">ai.google.dev</a></span>
              </div>
            )}

            {formData.provider === 'openrouter' && (
              <div className="flex items-center p-3 bg-purple-50 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300 rounded text-sm">
                <Info className="h-4 w-4 mr-2 flex-shrink-0" />
                <span>Get your API key from OpenRouter at <a href="https://openrouter.ai/" target="_blank" rel="noopener noreferrer" className="underline">openrouter.ai</a></span>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => handleDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={createCredentialMutation.isPending || updateCredentialMutation.isPending}
            >
              {(createCredentialMutation.isPending || updateCredentialMutation.isPending) && (
                <span className="mr-2 animate-spin">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                  </svg>
                </span>
              )}
              {editingCredential ? 'Update Credential' : 'Save Credential'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={handleDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action will permanently delete this credential. Any workflows using this credential may stop working.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (deletingCredentialId !== null) {
                  deleteCredentialMutation.mutate(deletingCredentialId);
                }
              }}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              {deleteCredentialMutation.isPending ? (
                <span className="mr-2 animate-spin">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M21 12a9 9 0 1 1-6.219-8.56" />
                  </svg>
                </span>
              ) : null}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default CredentialsPage;
