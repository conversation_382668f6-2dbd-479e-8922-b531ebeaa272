import React, { ReactNode, useState } from 'react';
import Sidebar from './Sidebar';
import { Menu, X } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

interface AppLayoutProps {
  children: ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const isMobile = useIsMobile();
  
  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <div className="h-screen flex overflow-hidden bg-neutral-100 dark:bg-neutral-900">
      {/* Mobile menu button */}
      {isMobile && (
        <button 
          onClick={toggleSidebar}
          className="fixed top-4 left-4 z-50 p-2 rounded-md bg-white dark:bg-neutral-800 shadow-md border border-neutral-200 dark:border-neutral-700"
          aria-label="Toggle menu"
        >
          {isSidebarOpen ? (
            <X className="w-5 h-5 text-neutral-600 dark:text-neutral-300" />
          ) : (
            <Menu className="w-5 h-5 text-neutral-600 dark:text-neutral-300" />
          )}
        </button>
      )}
      
      {/* Sidebar with responsive behavior */}
      <div className={`
        ${isMobile ? 'fixed inset-y-0 left-0 z-40 transition-transform transform duration-300 ease-in-out' : 'relative'}
        ${isMobile && !isSidebarOpen ? '-translate-x-full' : 'translate-x-0'}
      `}>
        <Sidebar onCloseMobile={isMobile ? () => setIsSidebarOpen(false) : undefined} />
      </div>
      
      {/* Main content */}
      <div className={`flex-1 flex flex-col h-full overflow-hidden ${isMobile ? 'w-full' : ''}`}>
        <div className="p-2 h-full overflow-auto">
          {children}
        </div>
      </div>
      
      {/* Overlay for mobile */}
      {isMobile && isSidebarOpen && (
        <div 
          className="fixed inset-0 bg-neutral-900/50 z-30"
          onClick={() => setIsSidebarOpen(false)}
          aria-hidden="true"
        />
      )}
    </div>
  );
};

export default AppLayout;
