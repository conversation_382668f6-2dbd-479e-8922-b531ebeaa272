import React from 'react';
import { Link } from 'wouter';
import { Star, Clock } from 'lucide-react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { Workflow } from '@/types/workflow';
import { formatDistanceToNow } from 'date-fns';

interface WorkflowItemProps {
  workflow?: Workflow;
  id?: number;
  name?: string;
  lastEdited?: string;
  status?: 'draft' | 'published';
  isFavorite?: boolean;
  nodeCount?: number;
  isActive?: boolean;
  linkToEditor?: boolean;
}

const WorkflowItem: React.FC<WorkflowItemProps> = ({
  workflow,
  id: propId,
  name: propName,
  lastEdited: propLastEdited,
  status: propStatus,
  isFavorite: propIsFavorite,
  nodeCount: propNodeCount,
  isActive: propIsActive,
  linkToEditor = false
}) => {
  const queryClient = useQueryClient();
  
  // Use either props or workflow object
  const id = workflow?.id || propId || 0;
  const name = workflow?.name || propName || '';
  const status = workflow?.status || propStatus || 'draft';
  const isFavorite = workflow?.isFavorite || propIsFavorite || false;
  const isActive = propIsActive || false;
  
  const lastEdited = workflow 
    ? formatDistanceToNow(new Date(workflow.updatedAt), { addSuffix: true })
    : propLastEdited || '';
  
  const nodeCount = workflow 
    ? Object.keys(workflow.nodes).length
    : propNodeCount || 0;
  
  // Toggle favorite mutation
  const toggleFavoriteMutation = useMutation({
    mutationFn: async () => {
      return apiRequest('POST', `/api/workflows/${id}/favorite`, {});
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/workflows'] });
    }
  });

  const handleToggleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    toggleFavoriteMutation.mutate();
  };

  const containerClasses = isActive
    ? 'p-3 bg-blue-50 dark:bg-blue-900/20 border-l-4 border-primary rounded cursor-pointer'
    : 'p-3 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 hover:border-primary dark:hover:border-primary rounded cursor-pointer';

  return (
    <Link href={linkToEditor ? `/workflows/editor/${id}` : `#`}>
      <div className={containerClasses}>
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-medium text-neutral-800 dark:text-white">{name}</h3>
            <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1 flex items-center">
              <Clock className="w-3 h-3 mr-1" />
              Last edited: {lastEdited}
            </p>
          </div>
          <button 
            onClick={handleToggleFavorite}
            className={`text-${isFavorite ? 'yellow' : 'neutral'}-500 hover:text-yellow-500`}
          >
            <Star className={`w-4 h-4 ${isFavorite ? 'fill-yellow-500 text-yellow-500' : ''}`} />
          </button>
        </div>
        <div className="flex items-center mt-2 text-xs">
          <span className={`px-2 py-0.5 rounded-full ${
            status === 'published' 
              ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' 
              : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
          }`}>
            {status === 'published' ? 'Published' : 'Draft'}
          </span>
          <span className="ml-2 text-neutral-500 dark:text-neutral-400">{nodeCount} nodes</span>
        </div>
      </div>
    </Link>
  );
};

export default WorkflowItem;
