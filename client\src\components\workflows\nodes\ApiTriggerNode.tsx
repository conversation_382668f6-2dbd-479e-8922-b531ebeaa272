import React from 'react';
import { NodeProps } from 'reactflow';
import BaseNode from './BaseNode';
import { Code } from 'lucide-react';

const ApiTriggerNode: React.FC<NodeProps> = ({ id, data, selected }) => {
  return (
    <BaseNode
      id={id}
      data={data}
      icon={<Code size={16} />}
      color="#5C2D91"
      hasInput={true}
      hasOutput={false}
      selected={selected}
    >
      <div className="mt-2 p-2 bg-neutral-50 dark:bg-neutral-900 rounded text-xs font-mono overflow-hidden">
        <div>{data.method || 'POST'} {data.url || 'https://api.example.com/endpoint'}</div>
        <div className="text-green-600 dark:text-green-400">
          {data.authType ? `✓ Authentication: ${data.authType === 'apiKey' ? 'API Key' : 'Bearer Token'}` : 'No authentication'}
        </div>
      </div>
      
      {data.lastResponse && (
        <div className="mt-2 flex items-center text-xs">
          <span className="px-1.5 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded">
            Response: {data.lastResponse.status || '200 OK'}
          </span>
        </div>
      )}
    </BaseNode>
  );
};

export default ApiTriggerNode;
