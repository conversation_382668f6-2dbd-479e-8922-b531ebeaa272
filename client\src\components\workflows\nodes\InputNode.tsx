import React from 'react';
import { NodeProps } from 'reactflow';
import BaseNode from './BaseNode';
import { ArrowRight } from 'lucide-react';

const InputNode: React.FC<NodeProps> = ({ id, data, selected, type, xPos, yPos, zIndex, isConnectable, targetPosition, sourcePosition, dragging }) => {
  return (
    <BaseNode
      id={id}
      data={data}
      icon={<ArrowRight size={16} />}
      color="#0078D4"
      hasInput={false}
      hasOutput={true}
      selected={selected}
      type={type}
      xPos={xPos}
      yPos={yPos}
      zIndex={zIndex}
      isConnectable={isConnectable}
      targetPosition={targetPosition}
      sourcePosition={sourcePosition}
      dragging={dragging}
    >
      <div className="mt-2 p-2 bg-neutral-50 dark:bg-neutral-900 rounded text-xs font-mono">
        {data.schema ? (
          JSON.stringify(data.schema, null, 2)
        ) : (
          <div>{`{"query": "string"}`}</div>
        )}
      </div>
    </BaseNode>
  );
};

export default InputNode;
