import { pgTable, text, serial, integer, boolean, jsonb, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// User schema (simplified for single user system)
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

// Workflow schema
export const workflows = pgTable("workflows", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description").notNull().default(''),
  nodes: jsonb("nodes").notNull().default({}),
  edges: jsonb("edges").notNull().default({}),
  status: text("status").notNull().default("draft"), // draft, published
  isFavorite: boolean("is_favorite").notNull().default(false),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const insertWorkflowSchema = createInsertSchema(workflows).pick({
  name: true,
  description: true,
  nodes: true,
  edges: true,
  status: true,
  isFavorite: true,
});

// Credentials schema
export const credentials = pgTable("credentials", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  provider: text("provider").notNull(), // google, openrouter, etc.
  apiKey: text("api_key").notNull(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
});

export const insertCredentialSchema = createInsertSchema(credentials).pick({
  name: true,
  provider: true,
  apiKey: true,
});

// Workflow runs schema
export const workflowRuns = pgTable("workflow_runs", {
  id: serial("id").primaryKey(),
  workflowId: integer("workflow_id").notNull().references(() => workflows.id),
  status: text("status").notNull(), // pending, running, completed, failed
  startTime: timestamp("start_time").notNull().defaultNow(),
  endTime: timestamp("end_time"),
  triggerType: text("trigger_type").notNull(), // manual, api
  input: jsonb("input").notNull().default({}),
  logs: jsonb("logs").notNull().default({}),
});

export const insertWorkflowRunSchema = createInsertSchema(workflowRuns).pick({
  workflowId: true,
  status: true,
  triggerType: true,
  input: true,
});

// Node run logs schema
export const nodeRuns = pgTable("node_runs", {
  id: serial("id").primaryKey(),
  workflowRunId: integer("workflow_run_id").notNull().references(() => workflowRuns.id),
  nodeId: text("node_id").notNull(),
  status: text("status").notNull(), // pending, running, completed, failed
  startTime: timestamp("start_time").notNull().defaultNow(),
  endTime: timestamp("end_time"),
  input: jsonb("input").notNull().default({}),
  output: jsonb("output").notNull().default({}),
  error: text("error"),
});

export const insertNodeRunSchema = createInsertSchema(nodeRuns).pick({
  workflowRunId: true,
  nodeId: true,
  status: true,
  input: true,
  output: true,
  error: true,
});

// Export types
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;

export type Workflow = typeof workflows.$inferSelect;
export type InsertWorkflow = z.infer<typeof insertWorkflowSchema>;

export type Credential = typeof credentials.$inferSelect;
export type InsertCredential = z.infer<typeof insertCredentialSchema>;

export type WorkflowRun = typeof workflowRuns.$inferSelect;
export type InsertWorkflowRun = z.infer<typeof insertWorkflowRunSchema>;

export type NodeRun = typeof nodeRuns.$inferSelect;
export type InsertNodeRun = z.infer<typeof insertNodeRunSchema>;
