import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";
import { queryClient } from "@/lib/queryClient";

// Prefetch initial data
const prefetchInitialData = async () => {
  try {
    await queryClient.prefetchQuery({
      queryKey: ['/api/workflows'],
    });
    await queryClient.prefetchQuery({
      queryKey: ['/api/credentials'],
    });
  } catch (error) {
    console.error("Failed to prefetch initial data:", error);
  }
};

// Initialize app
const init = async () => {
  await prefetchInitialData();
  createRoot(document.getElementById("root")!).render(<App />);
};

init();
