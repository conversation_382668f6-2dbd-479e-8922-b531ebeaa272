import React from 'react';
import { Link, useLocation } from 'wouter';
import { 
  Cpu, 
  History, 
  BarChart2, 
  <PERSON>, 
  Setting<PERSON>, 
  Star 
} from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';

interface SidebarProps {
  onCloseMobile?: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ onCloseMobile }) => {
  const isMobile = useIsMobile();
  const [location] = useLocation();

  const isActiveRoute = (route: string) => {
    if (route === '/workflows' && location === '/') {
      return true;
    }
    return location.startsWith(route);
  };

  const navItemClass = (route: string) => 
    `flex items-center px-4 py-2 ${isActiveRoute(route) 
      ? 'text-primary font-medium bg-blue-50 dark:bg-blue-900/20' 
      : 'text-neutral-600 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-800'}`;

  // Handle link clicks on mobile
  const handleLinkClick = () => {
    if (isMobile && onCloseMobile) {
      onCloseMobile();
    }
  };

  return (
    <div className={`w-64 h-full bg-white dark:bg-neutral-800 border-r border-neutral-200 dark:border-neutral-700 flex flex-col ${isMobile ? 'shadow-xl' : ''}`}>
      {/* App Logo */}
      <div className="p-4 border-b border-neutral-200 dark:border-neutral-700 flex items-center justify-between">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-primary rounded-md flex items-center justify-center text-white">
            <Cpu size={18} />
          </div>
          <h1 className="ml-3 font-semibold text-lg text-neutral-800 dark:text-white">AI Workflow</h1>
        </div>
      </div>
      
      {/* Main Menu */}
      <nav className="py-4">
        <ul className="space-y-1">
          <li>
            <Link href="/workflows">
              <a className={navItemClass('/workflows')} onClick={handleLinkClick}>
                <Cpu className="w-5 h-5 mr-3" />
                <span>Workflows</span>
              </a>
            </Link>
          </li>
          <li>
            <Link href="/run-history">
              <a className={navItemClass('/run-history')} onClick={handleLinkClick}>
                <History className="w-5 h-5 mr-3" />
                <span>Run History</span>
              </a>
            </Link>
          </li>
          <li>
            <Link href="/analytics">
              <a className={navItemClass('/analytics')} onClick={handleLinkClick}>
                <BarChart2 className="w-5 h-5 mr-3" />
                <span>Analytics</span>
              </a>
            </Link>
          </li>
          <li>
            <Link href="/credentials">
              <a className={navItemClass('/credentials')} onClick={handleLinkClick}>
                <Key className="w-5 h-5 mr-3" />
                <span>Credentials</span>
              </a>
            </Link>
          </li>
          <li>
            <Link href="/settings">
              <a className={navItemClass('/settings')} onClick={handleLinkClick}>
                <Settings className="w-5 h-5 mr-3" />
                <span>Settings</span>
              </a>
            </Link>
          </li>
        </ul>
      </nav>
      
      {/* Workflow List */}
      <div className="flex-1 overflow-y-auto scrollbar-thin border-t border-neutral-200 dark:border-neutral-700">
        <div className="px-4 py-3 flex justify-between items-center">
          <h2 className="font-medium text-neutral-800 dark:text-white">My Workflows</h2>
          <div className="flex items-center space-x-2">
            <Link href="/workflows/editor">
              <a className="p-1 hover:bg-neutral-100 dark:hover:bg-neutral-700 rounded" title="Add Workflow">
                <span className="text-primary">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M12 5v14M5 12h14" />
                  </svg>
                </span>
              </a>
            </Link>
            <button className="p-1 hover:bg-neutral-100 dark:hover:bg-neutral-700 rounded" title="Filter">
              <span className="text-neutral-500 dark:text-neutral-400">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3" />
                </svg>
              </span>
            </button>
          </div>
        </div>
        
        <div className="px-4 pb-3">
          <div className="flex space-x-2">
            <button className="px-3 py-1 bg-primary text-white text-xs rounded-full">
              All
            </button>
            <button className="px-3 py-1 bg-neutral-100 dark:bg-neutral-700 text-neutral-600 dark:text-neutral-300 text-xs rounded-full hover:bg-neutral-200 dark:hover:bg-neutral-600">
              Favorites
            </button>
            <button className="px-3 py-1 bg-neutral-100 dark:bg-neutral-700 text-neutral-600 dark:text-neutral-300 text-xs rounded-full hover:bg-neutral-200 dark:hover:bg-neutral-600">
              Published
            </button>
          </div>
        </div>
        
        <WorkflowList />
      </div>
      
      {/* User Info */}
      <div className="p-4 border-t border-neutral-200 dark:border-neutral-700 flex items-center">
        <div className="w-8 h-8 bg-accent rounded-full flex items-center justify-center text-white">
          <span className="font-medium">JS</span>
        </div>
        <div className="ml-3">
          <p className="text-sm font-medium text-neutral-800 dark:text-white">Developer</p>
          <p className="text-xs text-neutral-500 dark:text-neutral-400"><EMAIL></p>
        </div>
      </div>
    </div>
  );
};

// Simple WorkflowList component that will be populated by the WorkflowItem components
// In a real app, this would fetch data from the API
const WorkflowList: React.FC = () => {
  return (
    <div className="space-y-2 px-3">
      <WorkflowItem 
        id={1}
        name="Content Generator"
        lastEdited="2 hours ago"
        status="published"
        isFavorite={true}
        nodeCount={3}
        isActive={true}
      />
      
      <WorkflowItem 
        id={2}
        name="Email Responder"
        lastEdited="Yesterday"
        status="draft"
        isFavorite={false}
        nodeCount={5}
        isActive={false}
      />
      
      <WorkflowItem 
        id={3}
        name="Data Processor"
        lastEdited="3 days ago"
        status="published"
        isFavorite={true}
        nodeCount={7}
        isActive={false}
      />
      
      <WorkflowItem 
        id={4}
        name="API Integration"
        lastEdited="1 week ago"
        status="published"
        isFavorite={false}
        nodeCount={4}
        isActive={false}
      />
    </div>
  );
};

// WorkflowItem component to be imported from './WorkflowItem'
import WorkflowItem from '@/components/workflows/WorkflowItem';

export default Sidebar;
