import React, { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useTheme } from '@/components/ThemeProvider';
import { useToast } from '@/hooks/use-toast';
import { User, Settings as SettingsIcon, Moon, Sun, Database, Shield, BellRing, CloudOff, Save } from 'lucide-react';

const SettingsPage: React.FC = () => {
  const { theme, setTheme } = useTheme();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('general');
  
  const [userSettings, setUserSettings] = useState({
    username: 'developer',
    email: '<EMAIL>',
    notificationsEnabled: true,
    autoSaveEnabled: true,
    offlineMode: false
  });
  
  const [workflowSettings, setWorkflowSettings] = useState({
    defaultProvider: 'google',
    maxConcurrentRuns: '3',
    timeoutSeconds: '60',
    workflowCacheSize: '100'
  });
  
  const handleUserSettingChange = (field: string, value: any) => {
    setUserSettings(prev => ({ ...prev, [field]: value }));
  };
  
  const handleWorkflowSettingChange = (field: string, value: any) => {
    setWorkflowSettings(prev => ({ ...prev, [field]: value }));
  };
  
  const saveSettings = () => {
    toast({
      title: "Settings saved",
      description: "Your settings have been updated successfully",
    });
  };
  
  return (
    <div className="p-2">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-neutral-800 dark:text-white">Settings</h1>
      </div>
      
      <div className="grid grid-cols-12 gap-6">
        <div className="col-span-12 md:col-span-3">
          <Card>
            <CardContent className="p-0">
              <nav className="space-y-1 py-2">
                <button
                  className={`w-full px-4 py-2 text-left ${activeTab === 'general' ? 'bg-neutral-100 dark:bg-neutral-800 text-primary' : 'text-neutral-600 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-900'}`}
                  onClick={() => setActiveTab('general')}
                >
                  <div className="flex items-center">
                    <SettingsIcon className="h-4 w-4 mr-3" />
                    <span>General</span>
                  </div>
                </button>
                <button
                  className={`w-full px-4 py-2 text-left ${activeTab === 'appearance' ? 'bg-neutral-100 dark:bg-neutral-800 text-primary' : 'text-neutral-600 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-900'}`}
                  onClick={() => setActiveTab('appearance')}
                >
                  <div className="flex items-center">
                    <Sun className="h-4 w-4 mr-3" />
                    <span>Appearance</span>
                  </div>
                </button>
                <button
                  className={`w-full px-4 py-2 text-left ${activeTab === 'workflows' ? 'bg-neutral-100 dark:bg-neutral-800 text-primary' : 'text-neutral-600 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-900'}`}
                  onClick={() => setActiveTab('workflows')}
                >
                  <div className="flex items-center">
                    <Database className="h-4 w-4 mr-3" />
                    <span>Workflows</span>
                  </div>
                </button>
                <button
                  className={`w-full px-4 py-2 text-left ${activeTab === 'security' ? 'bg-neutral-100 dark:bg-neutral-800 text-primary' : 'text-neutral-600 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-900'}`}
                  onClick={() => setActiveTab('security')}
                >
                  <div className="flex items-center">
                    <Shield className="h-4 w-4 mr-3" />
                    <span>Security</span>
                  </div>
                </button>
              </nav>
            </CardContent>
          </Card>
        </div>
        
        <div className="col-span-12 md:col-span-9">
          {activeTab === 'general' && (
            <Card>
              <CardHeader>
                <CardTitle>General Settings</CardTitle>
                <CardDescription>
                  Manage your account and notification preferences
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Account Information</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="username">Username</Label>
                      <Input
                        id="username"
                        value={userSettings.username}
                        onChange={(e) => handleUserSettingChange('username', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={userSettings.email}
                        onChange={(e) => handleUserSettingChange('email', e.target.value)}
                      />
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Notifications</h3>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="notifications">Enable Notifications</Label>
                      <p className="text-sm text-neutral-500 dark:text-neutral-400">
                        Receive notifications for workflow completions and errors
                      </p>
                    </div>
                    <Switch
                      id="notifications"
                      checked={userSettings.notificationsEnabled}
                      onCheckedChange={(checked) => handleUserSettingChange('notificationsEnabled', checked)}
                    />
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Application Behavior</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="autosave">Auto-save Workflows</Label>
                        <p className="text-sm text-neutral-500 dark:text-neutral-400">
                          Automatically save workflow changes
                        </p>
                      </div>
                      <Switch
                        id="autosave"
                        checked={userSettings.autoSaveEnabled}
                        onCheckedChange={(checked) => handleUserSettingChange('autoSaveEnabled', checked)}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="offline">Offline Mode</Label>
                        <p className="text-sm text-neutral-500 dark:text-neutral-400">
                          Cache data locally for offline use
                        </p>
                      </div>
                      <Switch
                        id="offline"
                        checked={userSettings.offlineMode}
                        onCheckedChange={(checked) => handleUserSettingChange('offlineMode', checked)}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button onClick={saveSettings}>
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          )}
          
          {activeTab === 'appearance' && (
            <Card>
              <CardHeader>
                <CardTitle>Appearance</CardTitle>
                <CardDescription>
                  Customize the look and feel of the application
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Theme</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div
                      className={`border rounded-lg p-4 cursor-pointer transition-all ${
                        theme === 'light' 
                          ? 'border-primary bg-neutral-50 dark:bg-neutral-800' 
                          : 'border-neutral-200 dark:border-neutral-700'
                      }`}
                      onClick={() => setTheme('light')}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <Sun className="h-5 w-5 text-amber-500" />
                        <div className={`h-4 w-4 rounded-full ${theme === 'light' ? 'bg-primary' : 'border border-neutral-300 dark:border-neutral-600'}`} />
                      </div>
                      <h4 className="font-medium">Light</h4>
                      <p className="text-sm text-neutral-500 dark:text-neutral-400">
                        Light background with dark text
                      </p>
                    </div>
                    
                    <div
                      className={`border rounded-lg p-4 cursor-pointer transition-all ${
                        theme === 'dark' 
                          ? 'border-primary bg-neutral-50 dark:bg-neutral-800' 
                          : 'border-neutral-200 dark:border-neutral-700'
                      }`}
                      onClick={() => setTheme('dark')}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <Moon className="h-5 w-5 text-indigo-400" />
                        <div className={`h-4 w-4 rounded-full ${theme === 'dark' ? 'bg-primary' : 'border border-neutral-300 dark:border-neutral-600'}`} />
                      </div>
                      <h4 className="font-medium">Dark</h4>
                      <p className="text-sm text-neutral-500 dark:text-neutral-400">
                        Dark background with light text
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button onClick={saveSettings}>
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          )}
          
          {activeTab === 'workflows' && (
            <Card>
              <CardHeader>
                <CardTitle>Workflow Settings</CardTitle>
                <CardDescription>
                  Configure default behavior for workflow execution
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Default Settings</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="default-provider">Default AI Provider</Label>
                      <Select
                        value={workflowSettings.defaultProvider}
                        onValueChange={(value) => handleWorkflowSettingChange('defaultProvider', value)}
                      >
                        <SelectTrigger id="default-provider">
                          <SelectValue placeholder="Select provider" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="google">Google AI</SelectItem>
                          <SelectItem value="openrouter">OpenRouter</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="max-concurrent">Max Concurrent Runs</Label>
                      <Input
                        id="max-concurrent"
                        type="number"
                        min="1"
                        max="10"
                        value={workflowSettings.maxConcurrentRuns}
                        onChange={(e) => handleWorkflowSettingChange('maxConcurrentRuns', e.target.value)}
                      />
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Execution Settings</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="timeout">Default Timeout (seconds)</Label>
                      <Input
                        id="timeout"
                        type="number"
                        min="10"
                        max="300"
                        value={workflowSettings.timeoutSeconds}
                        onChange={(e) => handleWorkflowSettingChange('timeoutSeconds', e.target.value)}
                      />
                      <p className="text-xs text-neutral-500 dark:text-neutral-400">
                        Maximum time a workflow can run before timing out
                      </p>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="cache-size">Workflow Cache Size</Label>
                      <Input
                        id="cache-size"
                        type="number"
                        min="10"
                        max="1000"
                        value={workflowSettings.workflowCacheSize}
                        onChange={(e) => handleWorkflowSettingChange('workflowCacheSize', e.target.value)}
                      />
                      <p className="text-xs text-neutral-500 dark:text-neutral-400">
                        Number of workflow runs to keep in history
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button onClick={saveSettings}>
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          )}
          
          {activeTab === 'security' && (
            <Card>
              <CardHeader>
                <CardTitle>Security Settings</CardTitle>
                <CardDescription>
                  Manage security and privacy settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Password</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="current-password">Current Password</Label>
                      <Input
                        id="current-password"
                        type="password"
                        placeholder="••••••••"
                      />
                    </div>
                    <div></div>
                    <div className="space-y-2">
                      <Label htmlFor="new-password">New Password</Label>
                      <Input
                        id="new-password"
                        type="password"
                        placeholder="••••••••"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="confirm-password">Confirm New Password</Label>
                      <Input
                        id="confirm-password"
                        type="password"
                        placeholder="••••••••"
                      />
                    </div>
                  </div>
                  <Button className="mt-2" variant="outline">
                    Change Password
                  </Button>
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">API Security</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>API Key Masking</Label>
                        <p className="text-sm text-neutral-500 dark:text-neutral-400">
                          Always mask API keys when displaying credentials
                        </p>
                      </div>
                      <Switch
                        checked={true}
                        disabled
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label>Secure Credential Storage</Label>
                        <p className="text-sm text-neutral-500 dark:text-neutral-400">
                          Store credentials with encryption
                        </p>
                      </div>
                      <Switch
                        checked={true}
                        disabled
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button onClick={saveSettings}>
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
