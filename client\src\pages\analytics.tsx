import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { WorkflowRun, Workflow } from '@/types/workflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Bar<PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Legend
} from 'recharts';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { format, subDays } from 'date-fns';

const COLORS = ['var(--chart-1)', 'var(--chart-2)', 'var(--chart-3)', 'var(--chart-4)', 'var(--chart-5)'];

const Analytics: React.FC = () => {
  const [timeRange, setTimeRange] = useState<string>('30');
  
  // Fetch workflows
  const { data: workflows, isLoading: isWorkflowsLoading } = useQuery<Workflow[]>({
    queryKey: ['/api/workflows'],
  });
  
  // Fetch workflow runs
  const { data: workflowRuns, isLoading: isRunsLoading } = useQuery<WorkflowRun[]>({
    queryKey: ['/api/workflow-runs'],
  });
  
  // Calculate date range
  const endDate = new Date();
  const startDate = subDays(endDate, parseInt(timeRange));
  
  // Filter runs by date range
  const filteredRuns = workflowRuns?.filter(run => {
    const runDate = new Date(run.startTime);
    return runDate >= startDate && runDate <= endDate;
  }) || [];
  
  // Calculate workflow run statistics
  const workflowStats = React.useMemo(() => {
    if (!workflows || !filteredRuns) return [];
    
    const stats = workflows.map(workflow => {
      const workflowRuns = filteredRuns.filter(run => run.workflowId === workflow.id);
      const successfulRuns = workflowRuns.filter(run => run.status === 'completed');
      const failedRuns = workflowRuns.filter(run => run.status === 'failed');
      
      // Calculate average duration
      let totalDuration = 0;
      let runsWithDuration = 0;
      
      workflowRuns.forEach(run => {
        if (run.endTime) {
          const duration = new Date(run.endTime).getTime() - new Date(run.startTime).getTime();
          totalDuration += duration;
          runsWithDuration++;
        }
      });
      
      const avgDuration = runsWithDuration > 0 ? totalDuration / runsWithDuration / 1000 : 0;
      
      return {
        id: workflow.id,
        name: workflow.name,
        totalRuns: workflowRuns.length,
        successfulRuns: successfulRuns.length,
        failedRuns: failedRuns.length,
        successRate: workflowRuns.length > 0 ? (successfulRuns.length / workflowRuns.length) * 100 : 0,
        avgDuration: avgDuration.toFixed(1)
      };
    });
    
    return stats;
  }, [workflows, filteredRuns]);
  
  // Prepare data for daily runs chart
  const dailyRunsData = React.useMemo(() => {
    if (!filteredRuns) return [];
    
    const days = parseInt(timeRange);
    const result: {date: string, runs: number, successful: number, failed: number}[] = [];
    
    for (let i = 0; i < days; i++) {
      const date = subDays(new Date(), i);
      const dateStr = format(date, 'yyyy-MM-dd');
      const runsOnDate = filteredRuns.filter(run => 
        format(new Date(run.startTime), 'yyyy-MM-dd') === dateStr
      );
      
      result.unshift({
        date: format(date, 'MMM d'),
        runs: runsOnDate.length,
        successful: runsOnDate.filter(r => r.status === 'completed').length,
        failed: runsOnDate.filter(r => r.status === 'failed').length
      });
    }
    
    // Limit to last 30 days and filter out days with no data if there are too many
    return result.slice(-30).filter((d, i, arr) => 
      d.runs > 0 || i === 0 || i === arr.length - 1 || arr[i-1].runs > 0 || arr[i+1]?.runs > 0
    );
  }, [filteredRuns, timeRange]);
  
  // Status distribution for pie chart
  const statusDistribution = React.useMemo(() => {
    if (!filteredRuns) return [];
    
    const completed = filteredRuns.filter(run => run.status === 'completed').length;
    const failed = filteredRuns.filter(run => run.status === 'failed').length;
    const running = filteredRuns.filter(run => run.status === 'running').length;
    
    return [
      { name: 'Completed', value: completed },
      { name: 'Failed', value: failed },
      { name: 'Running', value: running }
    ].filter(item => item.value > 0);
  }, [filteredRuns]);
  
  // Statistics summary
  const summaryStats = React.useMemo(() => {
    if (!filteredRuns) return { total: 0, success: 0, failed: 0, successRate: 0 };
    
    const total = filteredRuns.length;
    const success = filteredRuns.filter(run => run.status === 'completed').length;
    const failed = filteredRuns.filter(run => run.status === 'failed').length;
    
    return {
      total,
      success,
      failed,
      successRate: total > 0 ? (success / total) * 100 : 0
    };
  }, [filteredRuns]);
  
  const isLoading = isWorkflowsLoading || isRunsLoading;
  
  return (
    <div className="p-2">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-neutral-800 dark:text-white">Analytics</h1>
        <div className="w-48">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger>
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-neutral-500 dark:text-neutral-400">Total Runs</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-10 w-20" />
            ) : (
              <div className="text-3xl font-bold">{summaryStats.total}</div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-neutral-500 dark:text-neutral-400">Success Rate</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-10 w-20" />
            ) : (
              <div className="text-3xl font-bold text-green-600 dark:text-green-500">
                {summaryStats.successRate.toFixed(1)}%
              </div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-neutral-500 dark:text-neutral-400">Successful Runs</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-10 w-20" />
            ) : (
              <div className="text-3xl font-bold text-green-600 dark:text-green-500">{summaryStats.success}</div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-neutral-500 dark:text-neutral-400">Failed Runs</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-10 w-20" />
            ) : (
              <div className="text-3xl font-bold text-red-600 dark:text-red-500">{summaryStats.failed}</div>
            )}
          </CardContent>
        </Card>
      </div>
      
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-3 mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="workflows">Workflow Performance</TabsTrigger>
          <TabsTrigger value="status">Status Distribution</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview">
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Daily Workflow Runs</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <Skeleton className="h-80 w-full" />
              ) : dailyRunsData.length > 0 ? (
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={dailyRunsData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip 
                        contentStyle={{ backgroundColor: 'var(--background)', borderColor: 'var(--border)' }} 
                      />
                      <Legend />
                      <Line 
                        type="monotone" 
                        dataKey="runs" 
                        name="Total Runs"
                        stroke="var(--chart-1)" 
                        activeDot={{ r: 8 }} 
                      />
                      <Line 
                        type="monotone" 
                        dataKey="successful" 
                        name="Successful" 
                        stroke="var(--chart-2)" 
                      />
                      <Line 
                        type="monotone" 
                        dataKey="failed" 
                        name="Failed" 
                        stroke="var(--chart-3)" 
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <div className="text-center py-20">
                  <p className="text-neutral-500 dark:text-neutral-400">No data available for the selected time range</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="workflows">
          <Card>
            <CardHeader>
              <CardTitle>Workflow Performance</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <Skeleton className="h-80 w-full" />
              ) : workflowStats.length > 0 ? (
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={workflowStats}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip 
                        contentStyle={{ backgroundColor: 'var(--background)', borderColor: 'var(--border)' }} 
                      />
                      <Legend />
                      <Bar dataKey="totalRuns" name="Total Runs" fill="var(--chart-1)" />
                      <Bar dataKey="successfulRuns" name="Successful" fill="var(--chart-2)" />
                      <Bar dataKey="failedRuns" name="Failed" fill="var(--chart-3)" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <div className="text-center py-20">
                  <p className="text-neutral-500 dark:text-neutral-400">No data available for the selected time range</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="status">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Run Status Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-80 w-full" />
                ) : statusDistribution.length > 0 ? (
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={statusDistribution}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={120}
                          fill="var(--chart-1)"
                          dataKey="value"
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        >
                          {statusDistribution.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip 
                          contentStyle={{ backgroundColor: 'var(--background)', borderColor: 'var(--border)' }} 
                        />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="text-center py-20">
                    <p className="text-neutral-500 dark:text-neutral-400">No data available for the selected time range</p>
                  </div>
                )}
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Average Execution Time</CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-80 w-full" />
                ) : workflowStats.length > 0 ? (
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={workflowStats}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        layout="vertical"
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis type="number" unit="s" />
                        <YAxis type="category" dataKey="name" />
                        <Tooltip 
                          contentStyle={{ backgroundColor: 'var(--background)', borderColor: 'var(--border)' }} 
                          formatter={(value) => [`${value} sec`, 'Avg Duration']}
                        />
                        <Bar dataKey="avgDuration" name="Avg Duration (sec)" fill="var(--chart-4)" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="text-center py-20">
                    <p className="text-neutral-500 dark:text-neutral-400">No data available for the selected time range</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Analytics;
